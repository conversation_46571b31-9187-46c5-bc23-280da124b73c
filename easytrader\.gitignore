site
cmd_cache.pk
bak
.mypy_cache
.pyre
.pytest_cache
yjb_account.json
htt.json
gft.json
test.py
ht_account.json
.idea
.vscode
.ipynb_checkpoints
Untitled.ipynb
untitled.txt
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
account.json
account.session
# C extensions
*.so

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*,cover

# Translations
*.mo
*.pot

# Django stuff:
*.log

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# cache
tmp/

secrets/
