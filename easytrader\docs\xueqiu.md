# 雪球组合模拟交易

因为雪球组合是按比例调仓的,所以模拟成券商实盘接口会有一些要注意的问题

* 接口基本与其他券商接口调用参数返回一致
* 委托单不支持挂高挂低(开盘时间都是直接市价成交的)
* 初始资金是按组合净值 1:1000000 换算来的, 可以通过 `easytrader.use('xq', initial_assets=初始资金值)` 来调整
* 委托单的委托价格和委托数量目前换算回来都是按1手拆的(雪球是按比例调仓的)
* 持仓价格和持仓数量问题同上, 但持股市值是对的.
* 一些不合理的操作会直接抛TradeError,注意看错误信息
          
----------------
20160909 新增函数adjust_weight，用于雪球组合比例调仓
             
adjust_weight函数包含两个参数，stock_code 指定调仓股票代码，weight 指定调仓比例      

